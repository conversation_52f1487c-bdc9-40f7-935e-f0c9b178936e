<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :deviceRid="deviceRid"
    :userRid="userRid"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestdata"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item :label="$t('dialog.terminalName')" prop="deviceRid">
        <DataTableElSelect v-model="deviceRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item :label="$t('dialog.userName')" prop="userRid">
        <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'

  import historyCommon from '@/components/common/historyCommon.vue'

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_user_check_in_history_list',
          cmd: 49,
        },
        dataTableName: 'shiftHistoryTable',
        deviceRid: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.deviceRid = ''
        this.userRid = ''
      },
      parseRequestdata(item) {
        var device = bfglob.gdevices.get(item.deviceId)
        if (typeof device === 'undefined') {
          bfglob.console.error('没有此对讲机', item.deviceId)
          return
        }
        item.orgShortName = device.orgShortName
        item.deviceSelfId = device.selfId
        item.userName = bfglob.guserData.getUserNameByKey(item.userId)

        return item
      },
    },
    components: {
      historyCommon,
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile ' : ''
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.terminalName'),
            data: 'deviceSelfId',
            width: '120px',
          },
          {
            title: this.$t('dataTable.readUser'),
            data: 'userName',
            width: this.isFR ? '160px' : '120px',
          },
          {
            title: this.$t('dataTable.readTime'),
            data: 'actionTime',
            width: this.isFR ? '160px' : '120px',
          },
          {
            title: this.$t('dataTable.type'),
            data: null,
            width: '100px',
            render: (data, type, row, meta) => {
              let actionTypes = ''
              switch (data.actionType) {
                case 1:
                  actionTypes = this.$t('dataTable.startWork')
                  break
                case 2:
                  actionTypes = this.$t('dataTable.punchInWork')
                  break
                case 3:
                  actionTypes = this.$t('dataTable.endWork')
                  break
              }

              return actionTypes
            },
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.readerCardHistory')
      },
    },
  }
</script>

<style></style>
