<template>
  <div id="gpsTrace" class="flex">
    <!--查询历史记录对话框-->
    <historyCommon
      v-show="!showTraceMap"
      ref="hisCom"
      :dbListName="queryProps.dbListName"
      :cmd="queryProps.cmd"
      :deviceRid="deviceRid"
      :userRid="userRid"
      :head="dthead"
      :name="dataTableName"
      :exportNamePrefix="dlgTitle"
      :parse-request-data="parseRequestdata"
      @data-change="dataChange"
      @remove-data-table-data="removeDataTableData"
    >
      <template #prefixButtons>
        <DataTableRowItem :enable="!isEmpty" iconFont="bfdx-guijixunhuan" @click="() => openPlayer()">
          <ellipsis-text class="mb-0" :content="$t('dialog.playTrack')" />
        </DataTableRowItem>
      </template>
      <template #optionsFormItem>
        <el-form-item :label="$t('dialog.terminalName')" prop="deviceRid">
          <DataTableElSelect v-model="deviceRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.rid" />
          </DataTableElSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.userName')" prop="userRid">
          <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
          </DataTableElSelect>
        </el-form-item>
      </template>
      <!--      <template slot="trackLaybackBtn">-->
      <!--        <el-button-->
      <!--            class="otherBtn flex-1"-->
      <!--            icon="view"-->
      <!--            type="primary"-->
      <!--            :disabled="traceBtnDisabled"-->
      <!--            @click="openPlayer"></el-button>-->
      <!--      </template>-->
    </historyCommon>
    <traceMap v-if="showTraceMap" class="flex-auto" :trace-data="$refs.hisCom.dataTableBody" trace-origin="gpsPlayer" @close="closeTraceMap" />
  </div>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'
  import traceMap from '@/components/common/traceMap.vue'

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_gps_history_list',
          cmd: 46,
        },
        dataTableName: 'gpstraceHistoryTable',
        deviceRid: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
        //展示轨迹地图
        showTraceMap: false,
        isEmpty: true,
      }
    },
    methods: {
      parseRequestdata(item) {
        var device = bfglob.gdevices.get(item.deviceId)
        if (typeof device === 'undefined') {
          bfglob.console.error('没有此对讲机', item.deviceId)
          return
        }
        item.orgShortName = device.orgShortName
        item.deviceSelfId = device.selfId
        item.userName = bfglob.guserData.getUserNameByKey(item.personId)

        item.showLon = item.lon.toFixed(4)
        item.showLat = item.lat.toFixed(4)
        item.speed = item.speed.toFixed(0)

        return item
      },
      openPlayer() {
        this.showTraceMap = true
      },
      closeTraceMap() {
        this.showTraceMap = false
        this.$nextTick(() => {
          // 退出轨迹回放地图后调整表格高度
          this.$refs.hisCom.$refs.datatable?.resetTableHeight()
          // 退出轨迹回放地图后调整表头
          this.$refs.hisCom.$refs.datatable?.columnsAdjust()
        })
      },
      dataChange(val) {
        this.isEmpty = val.length === 0
      },
      removeDataTableData() {
        this.deviceRid = ''
        this.userRid = ''
      },
    },
    mounted() {
      bfglob.on('open_vgpstraceHistory', function () {
        // 发布定位轨迹历史对话框加载完成
        bfglob.emit('vgpstraceHistory_loaded')
      })

      bfglob.emit('vgpstraceHistory_loaded')
    },
    components: {
      traceMap,
      historyCommon,
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile ' : ''
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.terminalName'),
            data: 'deviceSelfId',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.userName'),
            data: 'userName',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dataTable.gpsTime'),
            data: 'gpsTime',
            width: '120px',
          },
          {
            title: this.$t('dialog.lon'),
            data: 'showLon',
            width: '80px',
          },
          {
            title: this.$t('dialog.lat'),
            data: 'showLat',
            width: '80px',
          },
          {
            title: this.$t('dataTable.speed'),
            data: 'speed',
            width: '80px',
          },
          {
            title: this.$t('dataTable.direction'),
            data: 'direction',
            width: '80px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.GPSpathHistory')
      },
    },
  }
</script>

<style>
  #gpsTrace {
    height: calc(100vh - 68px);

    @media (max-width: 767px) {
      .dt-container > div:last-child {
        .btn-group {
          flex-wrap: nowrap !important;
        }
        .dt-search > input {
          margin-left: 0;
          width: 100%;
        }
      }
    }
  }

  .patrolPlayer-popupInfo {
    margin: 0;
    padding: 0;
    line-height: 20px;
  }

  #gpsTraceCtrl {
    position: absolute;
    top: 42px;
    left: 2px;
  }

  #gpsTraceCtrl .el-slider__runway {
    height: 8px;
    background: #fff;
  }

  #gpsTraceCtrl .el-slider__bar {
    height: 100%;
  }

  #gpsTraceCtrl .el-slider__button-wrapper {
    top: -14px;
  }

  #gpsTraceCtrl .el-slider__button {
    width: 16px;
    height: 16px;
  }

  #gpsTraceCtrl .el-button--mini {
    padding: 5px 15px;
    line-height: 16px;
  }

  .gps_trajectory {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ff3a3a;
    cursor: pointer;
  }
</style>
