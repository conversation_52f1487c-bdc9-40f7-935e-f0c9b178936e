<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4983115" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e9;</span>
                <div class="name">全呼</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6ea;</span>
                <div class="name">单呼</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e6;</span>
                <div class="name">对讲机麦克风</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e7;</span>
                <div class="name">对讲机组</div>
                <div class="code-name">&amp;#xe6e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e0;</span>
                <div class="name">对讲机内</div>
                <div class="code-name">&amp;#xe6e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e8;</span>
                <div class="name">集群</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e4;</span>
                <div class="name">对讲机切换</div>
                <div class="code-name">&amp;#xe6e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e1;</span>
                <div class="name">对讲机退出</div>
                <div class="code-name">&amp;#xe6e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6de;</span>
                <div class="name">表格删除按钮</div>
                <div class="code-name">&amp;#xe6de;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6da;</span>
                <div class="name">表格选择</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d9;</span>
                <div class="name">表格关闭</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6dc;</span>
                <div class="name">表格编辑按钮</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d7;</span>
                <div class="name">编辑样式2内部</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d8;</span>
                <div class="name">清除样式-内部</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6db;</span>
                <div class="name">新增样式内部</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e5;</span>
                <div class="name">组件底版</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e2;</span>
                <div class="name">对讲机通话</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6e3;</span>
                <div class="name">对讲机</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d1;</span>
                <div class="name">成功</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d5;</span>
                <div class="name">错误提示</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d0;</span>
                <div class="name">右操作</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6cf;</span>
                <div class="name">左操作</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d4;</span>
                <div class="name">操作1</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6cc;</span>
                <div class="name">关闭 1</div>
                <div class="code-name">&amp;#xe6cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6ce;</span>
                <div class="name">操作2 1</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d3;</span>
                <div class="name">操作3 (1)</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6d2;</span>
                <div class="name">链接.svg</div>
                <div class="code-name">&amp;#xe6d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c1;</span>
                <div class="name">通知消息</div>
                <div class="code-name">&amp;#xe6c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6cd;</span>
                <div class="name">Delete (删除)</div>
                <div class="code-name">&amp;#xe6cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c0;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe6c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c7;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c8;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe6c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c9;</span>
                <div class="name">选中</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6ca;</span>
                <div class="name">位置</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6cb;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe6cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6ba;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe6ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c3;</span>
                <div class="name">形状</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b9;</span>
                <div class="name">搜索用户</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c4;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe6c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c5;</span>
                <div class="name">搜索栏</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c6;</span>
                <div class="name">终端</div>
                <div class="code-name">&amp;#xe6c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b8;</span>
                <div class="name">轨迹循环</div>
                <div class="code-name">&amp;#xe6b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b7;</span>
                <div class="name">底板</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6c2;</span>
                <div class="name">外框</div>
                <div class="code-name">&amp;#xe6c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6bc;</span>
                <div class="name">账号名称</div>
                <div class="code-name">&amp;#xe6bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6bd;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe6bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6bb;</span>
                <div class="name">服务状态</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6be;</span>
                <div class="name">客户端</div>
                <div class="code-name">&amp;#xe6be;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6bf;</span>
                <div class="name">调度平台</div>
                <div class="code-name">&amp;#xe6bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6ad;</span>
                <div class="name">天线状态(正常)</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6ae;</span>
                <div class="name">信号接收器</div>
                <div class="code-name">&amp;#xe6ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6af;</span>
                <div class="name">风扇状态(正常)</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b0;</span>
                <div class="name">信号干扰(正常)</div>
                <div class="code-name">&amp;#xe6b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b1;</span>
                <div class="name">GPS状态(正常)</div>
                <div class="code-name">&amp;#xe6b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b2;</span>
                <div class="name">电压状态(正常)</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b3;</span>
                <div class="name">温度状态(正常)</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon bf-iconfont">&#xe6b4;</span>
                <div class="name">发射状态(正常)</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'bf-iconfont';
  src: url('iconfont.woff2?t=1755481969544') format('woff2'),
       url('iconfont.woff?t=1755481969544') format('woff'),
       url('iconfont.ttf?t=1755481969544') format('truetype'),
       url('iconfont.svg?t=1755481969544#bf-iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.bf-iconfont {
  font-family: "bf-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="bf-iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"bf-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-quanhu"></span>
            <div class="name">
              全呼
            </div>
            <div class="code-name">.bfdx-quanhu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-danhu"></span>
            <div class="name">
              单呼
            </div>
            <div class="code-name">.bfdx-danhu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangjimaikefeng"></span>
            <div class="name">
              对讲机麦克风
            </div>
            <div class="code-name">.bfdx-duijiangjimaikefeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangjizu"></span>
            <div class="name">
              对讲机组
            </div>
            <div class="code-name">.bfdx-duijiangjizu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangjinei"></span>
            <div class="name">
              对讲机内
            </div>
            <div class="code-name">.bfdx-duijiangjinei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-jiqun"></span>
            <div class="name">
              集群
            </div>
            <div class="code-name">.bfdx-jiqun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangjiqiehuan"></span>
            <div class="name">
              对讲机切换
            </div>
            <div class="code-name">.bfdx-duijiangjiqiehuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangjituichu"></span>
            <div class="name">
              对讲机退出
            </div>
            <div class="code-name">.bfdx-duijiangjituichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-biaogeshanchushannan"></span>
            <div class="name">
              表格删除按钮
            </div>
            <div class="code-name">.bfdx-biaogeshanchushannan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-biaogexuanze"></span>
            <div class="name">
              表格选择
            </div>
            <div class="code-name">.bfdx-biaogexuanze
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-biaogeguanbi"></span>
            <div class="name">
              表格关闭
            </div>
            <div class="code-name">.bfdx-biaogeguanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-biaogebianjianniu"></span>
            <div class="name">
              表格编辑按钮
            </div>
            <div class="code-name">.bfdx-biaogebianjianniu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-bianjiyangshi2neibu"></span>
            <div class="name">
              编辑样式2内部
            </div>
            <div class="code-name">.bfdx-bianjiyangshi2neibu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-qingchuyangshi-neibu"></span>
            <div class="name">
              清除样式-内部
            </div>
            <div class="code-name">.bfdx-qingchuyangshi-neibu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-xinzengyangshineibu"></span>
            <div class="name">
              新增样式内部
            </div>
            <div class="code-name">.bfdx-xinzengyangshineibu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-zujiandiban"></span>
            <div class="name">
              组件底版
            </div>
            <div class="code-name">.bfdx-zujiandiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangjitonghua"></span>
            <div class="name">
              对讲机通话
            </div>
            <div class="code-name">.bfdx-duijiangjitonghua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-duijiangji"></span>
            <div class="name">
              对讲机
            </div>
            <div class="code-name">.bfdx-duijiangji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-chenggong"></span>
            <div class="name">
              成功
            </div>
            <div class="code-name">.bfdx-chenggong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-cuowutishi"></span>
            <div class="name">
              错误提示
            </div>
            <div class="code-name">.bfdx-cuowutishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-youcaozuo"></span>
            <div class="name">
              右操作
            </div>
            <div class="code-name">.bfdx-youcaozuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-zuocaozuo"></span>
            <div class="name">
              左操作
            </div>
            <div class="code-name">.bfdx-zuocaozuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-caozuo11"></span>
            <div class="name">
              操作1
            </div>
            <div class="code-name">.bfdx-caozuo11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-guanbi1"></span>
            <div class="name">
              关闭 1
            </div>
            <div class="code-name">.bfdx-a-guanbi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-caozuo21"></span>
            <div class="name">
              操作2 1
            </div>
            <div class="code-name">.bfdx-a-caozuo21
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-caozuo31"></span>
            <div class="name">
              操作3 (1)
            </div>
            <div class="code-name">.bfdx-a-caozuo31
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-lianjiesvg"></span>
            <div class="name">
              链接.svg
            </div>
            <div class="code-name">.bfdx-a-lianjiesvg
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-tongzhixiaoxi"></span>
            <div class="name">
              通知消息
            </div>
            <div class="code-name">.bfdx-tongzhixiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-Deleteshanchu"></span>
            <div class="name">
              Delete (删除)
            </div>
            <div class="code-name">.bfdx-a-Deleteshanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-jian"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.bfdx-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.bfdx-shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-jia"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.bfdx-jia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-xuanzhong"></span>
            <div class="name">
              选中
            </div>
            <div class="code-name">.bfdx-xuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-weizhi"></span>
            <div class="name">
              位置
            </div>
            <div class="code-name">.bfdx-weizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-xiala"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.bfdx-xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-tianjia"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.bfdx-tianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-xingzhuang"></span>
            <div class="name">
              形状
            </div>
            <div class="code-name">.bfdx-xingzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-sousuoyonghu"></span>
            <div class="name">
              搜索用户
            </div>
            <div class="code-name">.bfdx-sousuoyonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.bfdx-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-sousuolan"></span>
            <div class="name">
              搜索栏
            </div>
            <div class="code-name">.bfdx-sousuolan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-zhongduan"></span>
            <div class="name">
              终端
            </div>
            <div class="code-name">.bfdx-zhongduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-guijixunhuan"></span>
            <div class="name">
              轨迹循环
            </div>
            <div class="code-name">.bfdx-guijixunhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-diban"></span>
            <div class="name">
              底板
            </div>
            <div class="code-name">.bfdx-diban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-waikuang1"></span>
            <div class="name">
              外框
            </div>
            <div class="code-name">.bfdx-waikuang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-zhanghaomingcheng"></span>
            <div class="name">
              账号名称
            </div>
            <div class="code-name">.bfdx-zhanghaomingcheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-mima"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.bfdx-mima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-fuwuzhuangtai"></span>
            <div class="name">
              服务状态
            </div>
            <div class="code-name">.bfdx-fuwuzhuangtai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-kehuduan"></span>
            <div class="name">
              客户端
            </div>
            <div class="code-name">.bfdx-kehuduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-tiaodupingtai"></span>
            <div class="name">
              调度平台
            </div>
            <div class="code-name">.bfdx-tiaodupingtai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-tianxianzhuangtaizhengchang"></span>
            <div class="name">
              天线状态(正常)
            </div>
            <div class="code-name">.bfdx-a-tianxianzhuangtaizhengchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-xinhaojieshouqi"></span>
            <div class="name">
              信号接收器
            </div>
            <div class="code-name">.bfdx-xinhaojieshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-fengshanzhuangtaizhengchang"></span>
            <div class="name">
              风扇状态(正常)
            </div>
            <div class="code-name">.bfdx-a-fengshanzhuangtaizhengchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-xinhaoganraozhengchang"></span>
            <div class="name">
              信号干扰(正常)
            </div>
            <div class="code-name">.bfdx-a-xinhaoganraozhengchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-GPSzhuangtaizhengchang"></span>
            <div class="name">
              GPS状态(正常)
            </div>
            <div class="code-name">.bfdx-a-GPSzhuangtaizhengchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-dianyazhuangtaizhengchang"></span>
            <div class="name">
              电压状态(正常)
            </div>
            <div class="code-name">.bfdx-a-dianyazhuangtaizhengchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-wenduzhuangtaizhengchang"></span>
            <div class="name">
              温度状态(正常)
            </div>
            <div class="code-name">.bfdx-a-wenduzhuangtaizhengchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon bf-iconfont bfdx-a-fashezhuangtaizhengchang"></span>
            <div class="name">
              发射状态(正常)
            </div>
            <div class="code-name">.bfdx-a-fashezhuangtaizhengchang
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="bf-iconfont bfdx-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            bf-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-quanhu"></use>
                </svg>
                <div class="name">全呼</div>
                <div class="code-name">#bfdx-quanhu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-danhu"></use>
                </svg>
                <div class="name">单呼</div>
                <div class="code-name">#bfdx-danhu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangjimaikefeng"></use>
                </svg>
                <div class="name">对讲机麦克风</div>
                <div class="code-name">#bfdx-duijiangjimaikefeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangjizu"></use>
                </svg>
                <div class="name">对讲机组</div>
                <div class="code-name">#bfdx-duijiangjizu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangjinei"></use>
                </svg>
                <div class="name">对讲机内</div>
                <div class="code-name">#bfdx-duijiangjinei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-jiqun"></use>
                </svg>
                <div class="name">集群</div>
                <div class="code-name">#bfdx-jiqun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangjiqiehuan"></use>
                </svg>
                <div class="name">对讲机切换</div>
                <div class="code-name">#bfdx-duijiangjiqiehuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangjituichu"></use>
                </svg>
                <div class="name">对讲机退出</div>
                <div class="code-name">#bfdx-duijiangjituichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-biaogeshanchushannan"></use>
                </svg>
                <div class="name">表格删除按钮</div>
                <div class="code-name">#bfdx-biaogeshanchushannan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-biaogexuanze"></use>
                </svg>
                <div class="name">表格选择</div>
                <div class="code-name">#bfdx-biaogexuanze</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-biaogeguanbi"></use>
                </svg>
                <div class="name">表格关闭</div>
                <div class="code-name">#bfdx-biaogeguanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-biaogebianjianniu"></use>
                </svg>
                <div class="name">表格编辑按钮</div>
                <div class="code-name">#bfdx-biaogebianjianniu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-bianjiyangshi2neibu"></use>
                </svg>
                <div class="name">编辑样式2内部</div>
                <div class="code-name">#bfdx-bianjiyangshi2neibu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-qingchuyangshi-neibu"></use>
                </svg>
                <div class="name">清除样式-内部</div>
                <div class="code-name">#bfdx-qingchuyangshi-neibu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-xinzengyangshineibu"></use>
                </svg>
                <div class="name">新增样式内部</div>
                <div class="code-name">#bfdx-xinzengyangshineibu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-zujiandiban"></use>
                </svg>
                <div class="name">组件底版</div>
                <div class="code-name">#bfdx-zujiandiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangjitonghua"></use>
                </svg>
                <div class="name">对讲机通话</div>
                <div class="code-name">#bfdx-duijiangjitonghua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-duijiangji"></use>
                </svg>
                <div class="name">对讲机</div>
                <div class="code-name">#bfdx-duijiangji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-chenggong"></use>
                </svg>
                <div class="name">成功</div>
                <div class="code-name">#bfdx-chenggong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-cuowutishi"></use>
                </svg>
                <div class="name">错误提示</div>
                <div class="code-name">#bfdx-cuowutishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-youcaozuo"></use>
                </svg>
                <div class="name">右操作</div>
                <div class="code-name">#bfdx-youcaozuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-zuocaozuo"></use>
                </svg>
                <div class="name">左操作</div>
                <div class="code-name">#bfdx-zuocaozuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-caozuo11"></use>
                </svg>
                <div class="name">操作1</div>
                <div class="code-name">#bfdx-caozuo11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-guanbi1"></use>
                </svg>
                <div class="name">关闭 1</div>
                <div class="code-name">#bfdx-a-guanbi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-caozuo21"></use>
                </svg>
                <div class="name">操作2 1</div>
                <div class="code-name">#bfdx-a-caozuo21</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-caozuo31"></use>
                </svg>
                <div class="name">操作3 (1)</div>
                <div class="code-name">#bfdx-a-caozuo31</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-lianjiesvg"></use>
                </svg>
                <div class="name">链接.svg</div>
                <div class="code-name">#bfdx-a-lianjiesvg</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-tongzhixiaoxi"></use>
                </svg>
                <div class="name">通知消息</div>
                <div class="code-name">#bfdx-tongzhixiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-Deleteshanchu"></use>
                </svg>
                <div class="name">Delete (删除)</div>
                <div class="code-name">#bfdx-a-Deleteshanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-jian"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#bfdx-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#bfdx-shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-jia"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#bfdx-jia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-xuanzhong"></use>
                </svg>
                <div class="name">选中</div>
                <div class="code-name">#bfdx-xuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-weizhi"></use>
                </svg>
                <div class="name">位置</div>
                <div class="code-name">#bfdx-weizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-xiala"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#bfdx-xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-tianjia"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#bfdx-tianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-xingzhuang"></use>
                </svg>
                <div class="name">形状</div>
                <div class="code-name">#bfdx-xingzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-sousuoyonghu"></use>
                </svg>
                <div class="name">搜索用户</div>
                <div class="code-name">#bfdx-sousuoyonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#bfdx-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-sousuolan"></use>
                </svg>
                <div class="name">搜索栏</div>
                <div class="code-name">#bfdx-sousuolan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-zhongduan"></use>
                </svg>
                <div class="name">终端</div>
                <div class="code-name">#bfdx-zhongduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-guijixunhuan"></use>
                </svg>
                <div class="name">轨迹循环</div>
                <div class="code-name">#bfdx-guijixunhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-diban"></use>
                </svg>
                <div class="name">底板</div>
                <div class="code-name">#bfdx-diban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-waikuang1"></use>
                </svg>
                <div class="name">外框</div>
                <div class="code-name">#bfdx-waikuang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-zhanghaomingcheng"></use>
                </svg>
                <div class="name">账号名称</div>
                <div class="code-name">#bfdx-zhanghaomingcheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-mima"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#bfdx-mima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-fuwuzhuangtai"></use>
                </svg>
                <div class="name">服务状态</div>
                <div class="code-name">#bfdx-fuwuzhuangtai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-kehuduan"></use>
                </svg>
                <div class="name">客户端</div>
                <div class="code-name">#bfdx-kehuduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-tiaodupingtai"></use>
                </svg>
                <div class="name">调度平台</div>
                <div class="code-name">#bfdx-tiaodupingtai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-tianxianzhuangtaizhengchang"></use>
                </svg>
                <div class="name">天线状态(正常)</div>
                <div class="code-name">#bfdx-a-tianxianzhuangtaizhengchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-xinhaojieshouqi"></use>
                </svg>
                <div class="name">信号接收器</div>
                <div class="code-name">#bfdx-xinhaojieshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-fengshanzhuangtaizhengchang"></use>
                </svg>
                <div class="name">风扇状态(正常)</div>
                <div class="code-name">#bfdx-a-fengshanzhuangtaizhengchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-xinhaoganraozhengchang"></use>
                </svg>
                <div class="name">信号干扰(正常)</div>
                <div class="code-name">#bfdx-a-xinhaoganraozhengchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-GPSzhuangtaizhengchang"></use>
                </svg>
                <div class="name">GPS状态(正常)</div>
                <div class="code-name">#bfdx-a-GPSzhuangtaizhengchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-dianyazhuangtaizhengchang"></use>
                </svg>
                <div class="name">电压状态(正常)</div>
                <div class="code-name">#bfdx-a-dianyazhuangtaizhengchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-wenduzhuangtaizhengchang"></use>
                </svg>
                <div class="name">温度状态(正常)</div>
                <div class="code-name">#bfdx-a-wenduzhuangtaizhengchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bfdx-a-fashezhuangtaizhengchang"></use>
                </svg>
                <div class="name">发射状态(正常)</div>
                <div class="code-name">#bfdx-a-fashezhuangtaizhengchang</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
